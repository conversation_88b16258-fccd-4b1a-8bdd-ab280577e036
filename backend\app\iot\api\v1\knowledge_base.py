#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库管理 API

提供知识库的CRUD操作接口，基于IoT认证系统进行权限控制
"""
import time
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from backend.common.log import log as logger

from backend.app.iot.schema.knowledge_base import (
    ErrorResponse,
    KnowledgeBaseCreate,
    KnowledgeBaseCreateResponse,
    KnowledgeBaseDelete,
    KnowledgeBaseDetailResponse,
    KnowledgeBaseErrorCodes,
    KnowledgeBaseInfo,
    KnowledgeBaseListResponse,
    KnowledgeBaseQuery,
    KnowledgeBaseStatsResponse,
    KnowledgeBaseUpdate
)
from backend.app.iot.service.knowledge_base_service import knowledge_base_service
from backend.common.response.response_schema import ResponseModel, response_base, CustomResponse
from backend.common.security.iot_permission import (
    DependsIoTRBAC,
    IoTPermissions,
    require_iot_permission
)
from backend.common.security.jwt import DependsJwtAuth

router = APIRouter()


@router.get(
    '/health',
    summary='知识库服务健康检查',
    response_model=ResponseModel
)
async def knowledge_base_health_check() -> ResponseModel:
    """
    检查知识库服务（RAGFlow）的连接状态

    不需要认证，用于系统监控
    """
    try:
        health_status = await knowledge_base_service.health_check()

        if health_status["status"] == "healthy":
            return response_base.success(
                res=CustomResponse(code=200, msg="知识库服务连接正常"),
                data=health_status
            )
        else:
            return response_base.fail(
                res=CustomResponse(code=500, msg="知识库服务连接异常"),
                data=health_status
            )

    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=500, msg=f"健康检查失败: {str(e)}")
        )


@router.post(
    '/datasets',
    summary='创建知识库',
    response_model=ResponseModel,
    dependencies=[
        Depends(require_iot_permission(IoTPermissions.KB_CREATE)),
        DependsJwtAuth,
        DependsIoTRBAC
    ]
)
async def create_knowledge_base(
    request: Request,
    kb_data: KnowledgeBaseCreate
) -> ResponseModel:
    """
    创建知识库
    
    需要 'iot:kb:create' 权限
    """
    try:
        user_id = str(request.user.id)
        kb_info = await knowledge_base_service.create_knowledge_base(kb_data, user_id)
        
        return response_base.success(
            res=CustomResponse(code=200, msg="知识库创建成功"),
            data=kb_info.model_dump()
        )

    except ValueError as e:
        return response_base.fail(
            res=CustomResponse(code=KnowledgeBaseErrorCodes.KB_NAME_EXISTS, msg=str(e))
        )
    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=KnowledgeBaseErrorCodes.INTERNAL_ERROR, msg=f"创建知识库失败: {str(e)}")
        )


@router.get(
    '/datasets',
    summary='获取知识库列表',
    response_model=ResponseModel,
    dependencies=[DependsJwtAuth]
)
async def list_knowledge_bases_auth(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(30, ge=1, le=100, description="每页大小"),
    orderby: str = Query("create_time", description="排序字段"),
    desc: bool = Query(True, description="是否降序"),
    name: Optional[str] = Query(None, description="名称过滤"),
    id: Optional[str] = Query(None, description="ID过滤")
) -> ResponseModel:
    """
    获取知识库列表（需要认证）

    需要 'iot:kb:list' 权限
    """
    try:
        user_id = str(request.user.id)
        query = KnowledgeBaseQuery(
            page=page,
            page_size=page_size,
            orderby=orderby,
            desc=desc,
            name=name,
            id=id
        )

        kb_list = await knowledge_base_service.list_knowledge_bases(query, user_id)

        return response_base.success(data=kb_list)

    except Exception as e:
        logger.error(f"获取知识库列表失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(code=KnowledgeBaseErrorCodes.QUERY_ERROR, msg=f"获取知识库列表失败: {str(e)}")
        )


@router.get(
    '/datasets/test',
    summary='获取知识库列表（测试版本）',
    response_model=ResponseModel
)
async def list_knowledge_bases(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(30, ge=1, le=100, description="每页大小"),
    orderby: str = Query("create_time", description="排序字段"),
    desc: bool = Query(True, description="是否降序"),
    name: Optional[str] = Query(None, description="名称过滤"),
    id: Optional[str] = Query(None, description="ID过滤")
) -> ResponseModel:
    """
    获取知识库列表
    
    需要 'iot:kb:list' 权限
    """
    try:
        user_id = str(request.user.id)
        query = KnowledgeBaseQuery(
            page=page,
            page_size=page_size,
            orderby=orderby,
            desc=desc,
            name=name,
            id=id
        )
        
        kb_list = await knowledge_base_service.list_knowledge_bases(query, user_id)
        
        return response_base.success(
            res=CustomResponse(code=200, msg="获取知识库列表成功"),
            data=[kb.model_dump() for kb in kb_list]
        )
        
    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=KnowledgeBaseErrorCodes.INTERNAL_ERROR, msg=f"获取知识库列表失败: {str(e)}")
        )


@router.get(
    '/datasets/{dataset_id}',
    summary='获取知识库详情',
    response_model=ResponseModel,
    dependencies=[
        Depends(require_iot_permission(IoTPermissions.KB_VIEW)),
        DependsJwtAuth,
        DependsIoTRBAC
    ]
)
async def get_knowledge_base(
    request: Request,
    dataset_id: str
) -> ResponseModel:
    """
    获取知识库详情
    
    需要 'iot:kb:view' 权限
    """
    try:
        user_id = str(request.user.id)
        kb_info = await knowledge_base_service.get_knowledge_base(dataset_id, user_id)
        
        if not kb_info:
            return response_base.fail(
                res=CustomResponse(code=KnowledgeBaseErrorCodes.KB_NOT_FOUND, msg="知识库不存在或无权限访问")
            )
        
        return response_base.success(
            res=CustomResponse(code=200, msg="获取知识库详情成功"),
            data=kb_info.model_dump()
        )
        
    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=KnowledgeBaseErrorCodes.INTERNAL_ERROR, msg=f"获取知识库详情失败: {str(e)}")
        )


@router.put(
    '/datasets/{dataset_id}',
    summary='更新知识库',
    response_model=ResponseModel,
    dependencies=[
        Depends(require_iot_permission(IoTPermissions.KB_EDIT)),
        DependsJwtAuth,
        DependsIoTRBAC
    ]
)
async def update_knowledge_base(
    request: Request,
    dataset_id: str,
    kb_data: KnowledgeBaseUpdate
) -> ResponseModel:
    """
    更新知识库
    
    需要 'iot:kb:edit' 权限
    """
    try:
        user_id = str(request.user.id)
        success = await knowledge_base_service.update_knowledge_base(dataset_id, kb_data, user_id)
        
        if not success:
            return response_base.fail(
                res=CustomResponse(code=KnowledgeBaseErrorCodes.KB_NOT_FOUND, msg="知识库不存在或无权限修改")
            )
        
        return response_base.success(
            res=CustomResponse(code=200, msg="知识库更新成功")
        )
        
    except ValueError as e:
        if "chunk_count" in str(e):
            return response_base.fail(
                res=CustomResponse(code=KnowledgeBaseErrorCodes.CHUNK_COUNT_NOT_ZERO, msg=str(e))
            )
        else:
            return response_base.fail(
                res=CustomResponse(code=KnowledgeBaseErrorCodes.KB_NAME_EXISTS, msg=str(e))
            )
    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=KnowledgeBaseErrorCodes.INTERNAL_ERROR, msg=f"更新知识库失败: {str(e)}")
        )


@router.delete(
    '/datasets',
    summary='删除知识库',
    response_model=ResponseModel,
    dependencies=[
        Depends(require_iot_permission(IoTPermissions.KB_DELETE)),
        DependsJwtAuth,
        DependsIoTRBAC
    ]
)
async def delete_knowledge_bases(
    request: Request,
    delete_data: KnowledgeBaseDelete
) -> ResponseModel:
    """
    删除知识库
    
    需要 'iot:kb:delete' 权限
    """
    try:
        user_id = str(request.user.id)
        success = await knowledge_base_service.delete_knowledge_bases(delete_data.ids, user_id)
        
        if success:
            return response_base.success(
                res=CustomResponse(code=200, msg="知识库删除成功")
            )
        else:
            return response_base.fail(
                res=CustomResponse(code=KnowledgeBaseErrorCodes.PERMISSION_DENIED, msg="删除失败，请检查权限")
            )

    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=KnowledgeBaseErrorCodes.INTERNAL_ERROR, msg=f"删除知识库失败: {str(e)}")
        )


@router.get(
    '/datasets/stats/summary',
    summary='获取知识库统计信息',
    response_model=ResponseModel,
    dependencies=[DependsJwtAuth]
)
async def get_knowledge_base_stats(request: Request) -> ResponseModel:
    """
    获取知识库统计信息
    
    需要 'iot:kb:stats' 权限
    """
    try:
        user_id = str(request.user.id)
        stats = await knowledge_base_service.get_knowledge_base_stats(user_id)
        
        return response_base.success(
            res=CustomResponse(code=200, msg="获取统计信息成功"),
            data=stats.model_dump()
        )
        
    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=KnowledgeBaseErrorCodes.INTERNAL_ERROR, msg=f"获取统计信息失败: {str(e)}")
        )


@router.get(
    '/test-no-auth',
    summary='测试端点（无认证）',
    response_model=ResponseModel
)
async def test_no_auth() -> ResponseModel:
    """
    测试端点，不需要认证
    """
    return response_base.success(
        res=CustomResponse(code=200, msg="测试成功，无需认证"),
        data={"timestamp": int(time.time() * 1000)}
    )


@router.get(
    '/datasets-test-auth',
    summary='知识库列表测试（有认证）',
    response_model=ResponseModel,
    dependencies=[DependsJwtAuth]
)
async def test_datasets_with_auth(request: Request) -> ResponseModel:
    """
    测试知识库列表，需要认证但不需要权限
    """
    try:
        user_id = str(request.user.id)
        username = request.user.username

        return response_base.success(
            res=CustomResponse(code=200, msg="认证测试成功"),
            data={
                "user_id": user_id,
                "username": username,
                "message": "JWT认证成功，用户信息获取正常"
            }
        )
    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=500, msg=f"认证测试失败: {str(e)}")
        )


@router.get(
    '/datasets-simple',
    summary='简单知识库列表（仅JWT认证）',
    response_model=ResponseModel,
    dependencies=[DependsJwtAuth]
)
async def simple_datasets_list(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页大小")
) -> ResponseModel:
    """
    简单的知识库列表，只需要JWT认证，不需要权限验证
    """
    try:
        user_id = str(request.user.id)
        query = KnowledgeBaseQuery(
            page=page,
            page_size=page_size,
            orderby="create_time",
            desc=True
        )

        kb_list = await knowledge_base_service.list_knowledge_bases(query, user_id)

        return response_base.success(
            res=CustomResponse(code=200, msg="获取知识库列表成功"),
            data=kb_list
        )

    except Exception as e:
        logger.error(f"获取知识库列表失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(code=500, msg=f"获取知识库列表失败: {str(e)}")
        )


@router.get(
    '/datasets-test',
    summary='知识库列表测试（无认证）',
    response_model=ResponseModel
)
async def test_datasets_no_auth() -> ResponseModel:
    """
    测试知识库列表，不需要认证
    """
    try:
        # 模拟用户ID
        user_id = "1"

        # 构造查询参数
        query = KnowledgeBaseQuery(
            page=1,
            page_size=10,
            orderby='create_time',
            desc=True,
            name=None,
            id=None
        )

        kb_list = await knowledge_base_service.list_knowledge_bases(query, user_id)

        return response_base.success(
            res=CustomResponse(code=200, msg="获取知识库列表成功（测试）"),
            data=[kb.model_dump() for kb in kb_list]
        )

    except Exception as e:
        logger.error(f"获取知识库列表失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(code=500, msg=f"获取知识库列表失败: {str(e)}")
        )


@router.get(
    '/datasets/health',
    summary='知识库服务健康检查',
    response_model=ResponseModel
)
async def datasets_health_check() -> ResponseModel:
    """
    知识库服务健康检查
    
    不需要认证，用于检查知识库服务是否正常运行
    """
    try:
        # 简单的健康检查
        health_status = {
            'status': 'healthy',
            'service': 'knowledge_base',
            'version': '1.0.0',
            'timestamp': int(time.time() * 1000)
        }
        
        return response_base.success(data=health_status)
        
    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=KnowledgeBaseErrorCodes.INTERNAL_ERROR, msg=f"健康检查失败: {str(e)}")
        )


@router.get(
    '/debug/cors',
    summary='CORS调试接口',
    response_model=ResponseModel
)
async def debug_cors(request: Request) -> ResponseModel:
    """
    CORS调试接口 - 检查跨域请求配置

    不需要认证，用于调试CORS配置
    """
    try:
        headers = dict(request.headers)
        origin = headers.get('origin', 'N/A')
        user_agent = headers.get('user-agent', 'N/A')

        debug_info = {
            'origin': origin,
            'user_agent': user_agent,
            'method': request.method,
            'url': str(request.url),
            'headers': {k: v for k, v in headers.items() if k.lower() not in ['authorization', 'cookie']},
            'cors_configured': True,
            'timestamp': int(time.time() * 1000)
        }

        return response_base.success(
            res=CustomResponse(code=200, msg="CORS调试信息获取成功"),
            data=debug_info
        )

    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=500, msg=f"CORS调试失败: {str(e)}")
        )


@router.get(
    '/debug/token',
    summary='Token调试接口',
    response_model=ResponseModel
)
async def debug_token(request: Request) -> ResponseModel:
    """
    Token调试接口 - 检查Token格式和认证状态

    不需要认证，用于调试Token问题
    """
    try:
        auth_header = request.headers.get('Authorization', '')
        cookie_token = request.cookies.get('token', '')

        debug_info = {
            'has_auth_header': bool(auth_header),
            'auth_header_format': 'Bearer xxx...' if auth_header.startswith('Bearer ') else auth_header[:20] + '...' if auth_header else 'N/A',
            'has_cookie_token': bool(cookie_token),
            'cookie_token_format': cookie_token[:20] + '...' if cookie_token else 'N/A',
            'headers_count': len(request.headers),
            'cookies_count': len(request.cookies),
            'timestamp': int(time.time() * 1000)
        }

        return response_base.success(
            res=CustomResponse(code=200, msg="Token调试信息获取成功"),
            data=debug_info
        )

    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=500, msg=f"Token调试失败: {str(e)}")
        )


@router.get(
    '/debug/auth-test',
    summary='认证测试接口',
    response_model=ResponseModel,
    dependencies=[DependsJwtAuth]
)
async def debug_auth_test(request: Request) -> ResponseModel:
    """
    认证测试接口 - 需要有效的JWT Token

    需要认证，用于测试认证是否正常工作
    """
    try:
        user_info = {
            'user_id': str(request.user.id),
            'username': request.user.username,
            'email': getattr(request.user, 'email', 'N/A'),
            'is_superuser': getattr(request.user, 'is_superuser', False),
            'is_staff': getattr(request.user, 'is_staff', False),
            'timestamp': int(time.time() * 1000)
        }

        return response_base.success(
            res=CustomResponse(code=200, msg="认证测试成功"),
            data=user_info
        )

    except Exception as e:
        return response_base.fail(
            res=CustomResponse(code=500, msg=f"认证测试失败: {str(e)}")
        )
