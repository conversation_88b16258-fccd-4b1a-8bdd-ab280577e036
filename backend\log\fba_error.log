2025-08-05 08:03:44.090 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\start_stable.py", line 12, in <module>
    uvicorn.run(
    │       └ <function run at 0x0000022A22138AE0>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000022A2213B9C0>
    └ <uvicorn.server.Server object at 0x0000022A289B2B40>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000022A2213BA60>
           │       │   └ <uvicorn.server.Server object at 0x0000022A289B2B40>
           │       └ <function run at 0x0000022A2196AFC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000022A28B5CD60>
           │      └ <function Runner.run at 0x0000022A21A07240>
           └ <asyncio.runners.Runner object at 0x0000022A28675520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000022A21A04E00>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000022A28675520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000022A21AD8CC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000022A21A06B60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000022A219647C0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1652, family=2, type=1, proto=6, laddr=('*************', 61347), raddr=('*************', 5981)>
    └ <_ProactorSocketTransport closing fd=1652>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-05 08:03:44.102 | ERROR    | c28c8b8480724cb4864d99d2ef6cd02b | 请求异常: 请求参数非法: func 字段为必填项，输入：None
2025-08-05 08:37:02.598 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to *************:6379. 信号灯超时时间已到.
2025-08-05 08:38:34.096 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 08:39:43.586 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 08:56:28.920 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 08:57:22.287 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 08:59:21.874 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 09:01:08.035 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 127.0.0.1:6379. 远程计算机拒绝网络连接。.
2025-08-05 09:02:31.717 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to *************:6379. 信号灯超时时间已到.
