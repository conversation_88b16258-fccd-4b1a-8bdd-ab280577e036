#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试启动脚本 - 跳过Redis连接检查
用于测试CORS和API接口功能
"""
import sys
import os
import uvicorn

def main():
    try:
        print("正在导入FastAPI应用...")
        
        # 设置环境变量，跳过Redis连接检查
        os.environ['SKIP_REDIS_CHECK'] = 'true'
        
        from backend.main import app
        print("✅ FastAPI应用导入成功")

        print("正在启动测试服务器...")
        print("🔧 注意: 跳过Redis连接检查，仅用于测试CORS和API接口")
        
        # 测试模式启动
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info",
            access_log=True,
            workers=1
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("详细错误信息:")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
